import { getGatewayChecklistItems } from '$lib/project_utils';
import { error, redirect } from '@sveltejs/kit';
import type { ServerLoad } from '@sveltejs/kit';

export const load: ServerLoad = async ({ params, locals }) => {
	const { supabase } = locals;
	const { client_name, project_name, org_name, stage_order } = params;

	if (!org_name || !client_name || !project_name) {
		throw redirect(303, '/');
	}

	// Combined query to fetch project with client, organization, and all project stages in one query
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select(
			`
			*,
			client!inner(
				*,
				organization!inner(name, org_id)
			),
			project_stage(*)
		`,
		)
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.single();

	if (projectError) {
		console.error('Error fetching project:', projectError);
		throw redirect(
			303,
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
		);
	}

	// Extract related data from the combined query
	const clientData = projectData.client;
	const allStages = projectData.project_stage || [];

	// Sort stages by stage_order
	allStages.sort((a, b) => a.stage_order - b.stage_order);

	// Find the requested stage
	const stageData = allStages.find((stage) => stage.stage_order === Number(stage_order));

	if (!stageData) {
		throw redirect(
			303,
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_name)}/stage-manager`,
		);
	}

	// Find the current active stage (first incomplete stage)
	const currentStage = allStages?.find((stage) => !stage.date_completed) || allStages?.[0];

	// Check if this stage is accessible (completed stages or current stage)
	const isAccessible =
		stageData.date_completed || (currentStage && stageData.stage_order <= currentStage.stage_order);

	// Get gateway checklist items for this stage
	const checklistItems = await getGatewayChecklistItems(supabase, stageData.project_stage_id);

	// Calculate checklist completion percentage
	const totalItems = checklistItems?.length || 0;
	const completedItems = checklistItems?.filter((item) => item.status === 'Complete')?.length || 0;
	const completionPercentage = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;

	return {
		client: clientData,
		project: projectData,
		stage: stageData,
		isAccessible,
		checklist: {
			items: checklistItems || [],
			total: totalItems,
			completed: completedItems,
			completionPercentage,
		},
	};
};
